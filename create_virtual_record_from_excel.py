import pandas as pd
import requests
import json
from datetime import datetime

# ==============================================================================
# 設定區 (CONFIGURATION)
# 請根據你的環境和需求修改以下變數
# ==============================================================================

# 你的後端 API 端點 URL
API_ENDPOINT = "https://v4.nexify.hk/api/objects/virtual-docs"  # <-- *** 請修改為你的 API URL ***

# 如果你的 API 需要授權，請填寫 Bearer Token
API_TOKEN = "8dc8b0943cff17a94bdba749fe932b40be6d9fc83ec9f37994334d52baf02302"  # <-- *** 如果需要，請填寫你的 API Token ***

# 要讀取的檔案路徑 (支援 Excel 和 CSV)
FILE_PATH = "marine_data.csv"  # <-- *** 請修改為你的檔案名稱 (支援 .xls, .xlsx, .csv) ***

# 虛擬記錄的父 ID (ParentID)
PARENT_ID = 48582  # <-- *** 請填寫正確的 Parent ID ***

# 物件類別 ID (ObjectClassID) - 海洋數據
OBJECT_CLASS_ID = 1458

# 分類層級 (ClassificationLevel)
CLASSIFICATION_LEVEL = 0

# 欄位索引 到 Property ID 的對應表 - 海洋數據
COLUMN_INDEX_TO_PROPERTY_ID_MAP = {
    0: 4581,  # 水質監測站編號 (water_quality_monitoring_station_number)
    1: 4582,  # 日期 (date)
    2: 4583,  # 取樣點 (sampling_point)
    3: 4584,  # 深度 (depth)
    4: 4585,  # 溶解氧(水面)(毫克/公升) (dissolved_oxygen_surface_mg_per_l)
    5: 4586,  # 溶解氧(水中)(毫克/公升) (dissolved_oxygen_mid_depth_mg_per_l)
    6: 4587,  # 溶解氧(水底)(毫克/公升) (dissolved_oxygen_bottom_mg_per_l)
    7: 4588,  # 五天生化需氧量(水面)(毫克/公升) (five_day_bod_surface_mg_per_l)
    8: 4589,  # 五天生化需氧量(水底)(毫克/公升) (five_day_bod_bottom_mg_per_l)
    9: 4590,  # 氨氮(水面)(毫克/公升) (ammonia_nitrogen_surface_mg_per_l)
    10: 4591, # 氨氮(水底)(毫克/公升) (ammonia_nitrogen_bottom_mg_per_l)
    11: 4592, # 無機氮(水面)(毫克/公升) (inorganic_nitrogen_surface_mg_per_l)
    12: 4593, # 無機氮(水底)(毫克/公升) (inorganic_nitrogen_bottom_mg_per_l)
    13: 4594, # 正磷酸鹽(水面)(毫克/公升) (orthophosphate_surface_mg_per_l)
    14: 4595, # 正磷酸鹽(水底)(毫克/公升) (orthophosphate_bottom_mg_per_l)
    15: 4596, # 懸浮固體(水面)(毫克/公升) (suspended_solids_surface_mg_per_l)
    16: 4597, # 懸浮固體(水底)(毫克/公升) (suspended_solids_bottom_mg_per_l)
    17: 4598, # 大腸桿菌(每百毫升個) (e_coli_count_per_100ml)
    18: 4599, # 葉綠素-a(微克/公升) (chlorophyll_a_ug_per_l)
    19: 4600, # 酸鹼值(水面) (ph_value_surface)
    20: 4601, # 酸鹼值(水底) (ph_value_bottom)
    21: 4602, # 鹽度(千分之一) (salinity_ppt)
    22: 4603, # 水溫(攝氏度) (water_temperature_celsius)
}

# 數值型欄位 (FieldType = 1) - 需要特殊處理
NUMERIC_PROPERTY_IDS = {4585, 4586, 4587, 4588, 4589, 4590, 4591, 4592, 4593, 4594, 4595, 4596, 4597, 4598, 4599, 4600, 4601, 4602, 4603}

# 日期型欄位 (FieldType = 2) - 需要特殊處理
DATE_PROPERTY_IDS = {4582}


# ==============================================================================
# 主要腳本邏輯 (MAIN SCRIPT LOGIC)
# 通常不需要修改以下內容
# ==============================================================================

def parse_date_value(value):
    """
    解析日期值，支援多種日期格式
    """
    if pd.isna(value) or str(value).strip() == "":
        return None

    date_str = str(value).strip()

    # 嘗試多種日期格式
    date_formats = [
        "%Y-%m-%d",
        "%Y/%m/%d",
        "%d/%m/%Y",
        "%d-%m-%Y",
        "%Y-%m-%d %H:%M:%S",
        "%Y/%m/%d %H:%M:%S"
    ]

    for fmt in date_formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            return parsed_date.strftime("%Y-%m-%d")
        except ValueError:
            continue

    # 如果都無法解析，返回原始值
    print(f"警告：無法解析日期格式 '{date_str}'，將使用原始值")
    return date_str

def parse_numeric_value(value):
    """
    解析數值，處理空值和非數值情況
    """
    if pd.isna(value) or str(value).strip() == "":
        return None

    try:
        # 嘗試轉換為浮點數
        numeric_value = float(str(value).strip())
        return str(numeric_value)
    except ValueError:
        # 如果無法轉換為數值，返回 None (不包含此屬性)
        print(f"警告：無法解析數值 '{value}'，將跳過此欄位")
        return None

def create_virtual_docs_from_excel():
    """
    從檔案讀取資料，並呼叫 API 建立虛擬記錄。支援 Excel (.xls, .xlsx) 和 CSV 檔案。
    """
    try:
        # 根據檔案副檔名決定讀取方式
        file_extension = FILE_PATH.lower().split('.')[-1]

        if file_extension == 'csv':
            # 讀取 CSV 檔案
            df = pd.read_csv(FILE_PATH, header=0)
            print(f"成功讀取 CSV 檔案 '{FILE_PATH}'，找到 {len(df)} 筆記錄。")
            use_iloc = True  # CSV 使用 iloc 索引
        elif file_extension in ['xls', 'xlsx']:
            # 讀取 Excel 檔案
            df = pd.read_excel(FILE_PATH, header=None, skiprows=1)
            print(f"成功讀取 Excel 檔案 '{FILE_PATH}'，找到 {len(df)} 筆記錄。(已跳過標頭)")
            use_iloc = False  # Excel 使用數字索引
        else:
            print(f"錯誤：不支援的檔案格式 '{file_extension}'。請使用 .csv, .xls 或 .xlsx 檔案。")
            return

        print(f"檔案欄位數量: {len(df.columns)}")
        if file_extension == 'csv':
            print(f"CSV 欄位名稱: {list(df.columns)}")

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{FILE_PATH}'。")
        return
    except Exception as e:
        print(f"讀取檔案時發生錯誤：{e}")
        return

    headers = {
        "Content-Type": "application/json",
        "apikey": f"{API_TOKEN}" if API_TOKEN else ""
    }

    success_count = 0
    error_count = 0

    for index, row in df.iterrows():
        properties_list = []

        for col_index, prop_id in COLUMN_INDEX_TO_PROPERTY_ID_MAP.items():
            if col_index < len(row):
                # 根據檔案類型選擇正確的索引方式
                if file_extension == 'csv':
                    value = row.iloc[col_index]
                else:
                    value = row[col_index]

                processed_value = None

                # 根據屬性類型處理值
                if prop_id in DATE_PROPERTY_IDS:
                    processed_value = parse_date_value(value)
                elif prop_id in NUMERIC_PROPERTY_IDS:
                    processed_value = parse_numeric_value(value)
                else:
                    # 文字型欄位
                    if not pd.isna(value) and str(value).strip() != "":
                        processed_value = str(value).strip()

                # 如果處理後的值不為空，加入屬性列表
                if processed_value is not None:
                    property_data = {
                        "PropertyID": prop_id,
                        "Value": processed_value
                    }
                    properties_list.append(property_data)

        # 如果整行都是空的，跳過這筆記錄
        if not properties_list:
            print("-" * 50)
            print(f"正在跳過第 {index + 1} 行，因為所有對應的欄位都是空的。")
            continue

        # 建立記錄名稱 (針對海洋數據：使用監測站編號 + 日期 + 取樣點)
        if file_extension == 'csv':
            station_number = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else "Unknown"
            date_value = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else "Unknown"
            sampling_point = str(row.iloc[2]) if not pd.isna(row.iloc[2]) else "Unknown"
        else:
            station_number = str(row[0]) if not pd.isna(row[0]) else "Unknown"
            date_value = str(row[1]) if not pd.isna(row[1]) else "Unknown"
            sampling_point = str(row[2]) if not pd.isna(row[2]) else "Unknown"

        doc_name = f"Marine_Data_{station_number}_{date_value}_{sampling_point}_{index + 1}"

        payload = {
            "Name": doc_name,
            "ParentID": PARENT_ID,
            "ObjectClassID": OBJECT_CLASS_ID,
            "ClassificationLevel": CLASSIFICATION_LEVEL,
            "Properties": properties_list
        }

        print("-" * 50)
        print(f"正在處理第 {index + 1} 筆記錄：{doc_name} (包含 {len(properties_list)} 個非空屬性)")
        # 為了偵錯，可以取消以下註解來印出 payload
        # print("傳送的 Payload:", json.dumps(payload, indent=2, ensure_ascii=False))

        try:
            response = requests.post(API_ENDPOINT, headers=headers, json=payload, timeout=30)

            if 200 <= response.status_code < 300:
                new_id = response.text
                print(f"  \033[92m成功！\033[0m 記錄 '{doc_name}' 已建立。狀態碼: {response.status_code}，新 ID: {new_id}")
                success_count += 1
            else:
                print(f"  \033[91m失敗！\033[0m 記錄 '{doc_name}' 建立失敗。狀態碼: {response.status_code}")
                print(f"  回應內容: {response.text}")
                error_count += 1

        except requests.exceptions.RequestException as e:
            print(f"  \033[91m請求錯誤！\033[0m 呼叫 API 時發生錯誤: {e}")
            error_count += 1

    print("=" * 60)
    print(f"處理完成！成功建立 {success_count} 筆記錄，{error_count} 筆失敗。")

if __name__ == "__main__":
    create_virtual_docs_from_excel()