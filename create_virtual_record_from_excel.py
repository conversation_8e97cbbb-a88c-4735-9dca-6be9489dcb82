import pandas as pd
import requests
import json

# ==============================================================================
# 設定區 (CONFIGURATION)
# 請根據你的環境和需求修改以下變數
# ==============================================================================

# 你的後端 API 端點 URL
API_ENDPOINT = "https://v4.nexify.hk/api/objects/virtual-docs"  # <-- *** 請修改為你的 API URL ***

# 如果你的 API 需要授權，請填寫 Bearer Token
API_TOKEN = "8dc8b0943cff17a94bdba749fe932b40be6d9fc83ec9f37994334d52baf02302"  # <-- *** 如果需要，請填寫你的 API Token ***

# 要讀取的 Excel 檔案路徑
EXCEL_FILE_PATH = "APP_2025_Q1.xls" # <-- *** 請修改為你的 Excel 檔案名稱 ***

# 虛擬記錄的父 ID (ParentID)
PARENT_ID = 48582  # <-- *** 請填寫正確的 Parent ID ***

# 物件類別 ID (ObjectClassID)
OBJECT_CLASS_ID = 1372

# 分類層級 (ClassificationLevel)
CLASSIFICATION_LEVEL = 0

# Excel 欄位名稱 到 後端 PropertyKeyName 的對應表
# !!! 重要：請確保 Excel 檔案中的欄位名稱與此處的 "鍵" (左側) 完全一致 !!!
COLUMN_INDEX_TO_PROPERTY_ID_MAP = {
    0: 3939,  # A欄 (申請編號) -> Property ID 3939
    1: 3940,  # B欄 (土地用途地帶) -> Property ID 3940
    2: 3941,
    3: 3942,  # C欄 (Applied Use/Development) -> Property ID 3942 (applicationUse_eng)
    4: 3943,
    5: 3944,  # D欄 (Address/Land Lot) -> Property ID 3944 (address_eng)
    6: 3945,  # E欄 (地盤面積) -> Property ID 3945
    7: 3946,  # F欄 (決定的日期) -> Property ID 3946
    8: 3947,
    9: 3948,   # G欄 (Decision) -> Property ID 3948 (decision_eng)
    
    # 根據你的 Excel 欄位順序和 Property ID 填寫
    # 如果要同時填入中文的 Property，可以像這樣
    # 2: 3941, # C欄 -> Property ID 3941 (applicationUse_chi)
}


# ==============================================================================
# 主要腳本邏輯 (MAIN SCRIPT LOGIC)
# 通常不需要修改以下內容
# ==============================================================================

def create_virtual_docs_from_excel():
    """
    從 Excel 檔案讀取資料，並呼叫 API 建立虛擬記錄。
    """
    try:
        # 讀取 .xls 或 .xlsx。請確保已安裝 xlrd (`pip install xlrd`) 來讀取 .xls
        df = pd.read_excel(EXCEL_FILE_PATH, header=None, skiprows=1)
        print(f"成功讀取 Excel 檔案 '{EXCEL_FILE_PATH}'，找到 {len(df)} 筆記錄。(已跳過標頭)")
    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{EXCEL_FILE_PATH}'。")
        return
    except Exception as e:
        print(f"讀取 Excel 檔案時發生錯誤：{e}")
        return

    headers = {
        "Content-Type": "application/json",
        "apikey": f"{API_TOKEN}" if API_TOKEN else ""
    }

    for index, row in df.iterrows():
        properties_list = []
        
        for col_index, prop_id in COLUMN_INDEX_TO_PROPERTY_ID_MAP.items():
            if col_index < len(row):
                value = row[col_index]
                
                # ============================================================
                # (*** 主要修改區域 ***)
                # 檢查值是否為空。如果不是空值 (非NaN且去除空格後不為空)，才加入列表
                # ============================================================
                if not pd.isna(value) and str(value).strip() != "":
                    property_data = {
                        "PropertyID": prop_id,
                        "Value": str(value).strip() # .strip() 順便去除前後多餘的空格
                    }
                    properties_list.append(property_data)
        
        # 如果整行都是空的，properties_list 也會是空的，可能不需要建立這筆記錄
        if not properties_list:
            print("-" * 50)
            print(f"正在跳過第 {index + 1} 行，因為所有對應的欄位都是空的。")
            continue

        doc_name_column_index = 0
        doc_name = str(row.get(doc_name_column_index, f"Record_{index + 1}"))

        payload = {
            "Name": doc_name,
            "ParentID": PARENT_ID,
            "ObjectClassID": OBJECT_CLASS_ID,
            "ClassificationLevel": CLASSIFICATION_LEVEL,
            "Properties": properties_list
        }

        print("-" * 50)
        print(f"正在處理第 {index + 1} 筆記錄：{doc_name} (包含 {len(properties_list)} 個非空屬性)")
        # 為了偵錯，可以取消以下註解來印出 payload
        # print("傳送的 Payload:", json.dumps(payload, indent=2, ensure_ascii=False))

        try:
            response = requests.post(API_ENDPOINT, headers=headers, json=payload, timeout=30)
            
            if 200 <= response.status_code < 300:
                new_id = response.text
                print(f"  \033[92m成功！\033[0m 記錄 '{doc_name}' 已建立。狀態碼: {response.status_code}，新 ID: {new_id}")
            else:
                print(f"  \033[91m失敗！\033[0m 記錄 '{doc_name}' 建立失敗。狀態碼: {response.status_code}")
                print(f"  回應內容: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"  \033[91m請求錯誤！\033[0m 呼叫 API 時發生錯誤: {e}")

if __name__ == "__main__":
    create_virtual_docs_from_excel()